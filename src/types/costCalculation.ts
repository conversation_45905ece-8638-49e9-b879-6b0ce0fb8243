
export interface ExpenseCategory {
  id: string;
  name: string;
  iconName: string;
  monthlyAmount: number;
  frequency: 'monthly' | 'yearly' | 'one-time';
  yearsInterval?: number;
  isCustom?: boolean;
}

export interface CostCalculationProps {
  retirementAge: number;
  inflationRate: number;
}

export interface CostResults {
  currentMonthlyCost: number;
  currentAnnualCost: number;
  adjustedMonthlyCost: number;
  adjustedAnnualCost: number;
  totalRetirementCost: number;
  oneTimeCosts: number;
  breakdown: {
    monthly: number;
    yearly: number;
    oneTime: number;
  };
}
