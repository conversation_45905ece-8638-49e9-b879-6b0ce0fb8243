
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Calculator, Plus } from 'lucide-react';
import { CostCalculationProps } from '@/types/costCalculation';
import { useCostCalculation } from '@/hooks/useCostCalculation';
import ExpenseCard from './cost-calculation/ExpenseCard';
import AddExpenseForm from './cost-calculation/AddExpenseForm';
import CostResults from './cost-calculation/CostResults';

const CostCalculation = ({ retirementAge, inflationRate }: CostCalculationProps) => {
  const {
    expenses,
    retirementDuration,
    results,
    showAddForm,
    newExpense,
    setRetirementDuration,
    calculateCosts,
    setShowAddForm,
    setNewExpense,
    addCustomExpense,
    handleExpenseChange,
    handleRemoveExpense
  } = useCostCalculation(retirementAge, inflationRate);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="w-5 h-5" />
            Retirement Cost Calculator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4 mb-6">
            <div>
              <Label htmlFor="retirementDuration">Expected Retirement Duration (Years)</Label>
              <Input
                id="retirementDuration"
                type="number"
                value={retirementDuration}
                onChange={(e) => setRetirementDuration(parseFloat(e.target.value) || 25)}
                className="mt-1"
              />
            </div>
            <div className="flex items-end">
              <Button 
                onClick={() => setShowAddForm(!showAddForm)} 
                variant="outline" 
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Custom Expense
              </Button>
            </div>
          </div>

          {showAddForm && (
            <AddExpenseForm
              newExpense={newExpense}
              onExpenseChange={setNewExpense}
              onAdd={addCustomExpense}
              onCancel={() => setShowAddForm(false)}
            />
          )}

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {expenses.map((expense) => (
              <ExpenseCard
                key={expense.id}
                expense={expense}
                onExpenseChange={handleExpenseChange}
                onRemove={expense.isCustom ? handleRemoveExpense : undefined}
              />
            ))}
          </div>

          <Button onClick={calculateCosts} className="w-full mt-6">
            Calculate Retirement Costs
          </Button>
        </CardContent>
      </Card>

      {results && (
        <CostResults
          results={results}
          inflationRate={inflationRate}
          retirementDuration={retirementDuration}
        />
      )}
    </div>
  );
};

export default CostCalculation;
