
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { ExpenseCategory } from '@/types/costCalculation';

interface AddExpenseFormProps {
  newExpense: Partial<ExpenseCategory>;
  onExpenseChange: (expense: Partial<ExpenseCategory>) => void;
  onAdd: () => void;
  onCancel: () => void;
}

const AddExpenseForm = ({ newExpense, onExpenseChange, onAdd, onCancel }: AddExpenseFormProps) => {
  return (
    <Card className="p-4 mb-4 border-dashed">
      <h3 className="font-semibold mb-3">Add Custom Expense</h3>
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <Label className="text-xs">Expense Name</Label>
          <Input
            value={newExpense.name || ''}
            onChange={(e) => onExpenseChange({...newExpense, name: e.target.value})}
            placeholder="e.g., Pet Care"
            className="h-8"
          />
        </div>
        <div>
          <Label className="text-xs">Amount (₹)</Label>
          <Input
            type="number"
            value={newExpense.monthlyAmount || ''}
            onChange={(e) => onExpenseChange({...newExpense, monthlyAmount: parseFloat(e.target.value) || 0})}
            className="h-8"
          />
        </div>
        <div>
          <Label className="text-xs">Frequency</Label>
          <select
            value={newExpense.frequency || 'monthly'}
            onChange={(e) => onExpenseChange({...newExpense, frequency: e.target.value as any})}
            className="w-full h-8 px-2 border border-gray-300 rounded text-sm"
          >
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
            <option value="one-time">One-time</option>
          </select>
        </div>
        {newExpense.frequency === 'one-time' && (
          <div>
            <Label className="text-xs">Every X Years</Label>
            <Input
              type="number"
              value={newExpense.yearsInterval || 1}
              onChange={(e) => onExpenseChange({...newExpense, yearsInterval: parseFloat(e.target.value) || 1})}
              className="h-8"
            />
          </div>
        )}
      </div>
      <div className="flex gap-2 mt-3">
        <Button onClick={onAdd} size="sm">Add Expense</Button>
        <Button onClick={onCancel} variant="outline" size="sm">Cancel</Button>
      </div>
    </Card>
  );
};

export default AddExpenseForm;
