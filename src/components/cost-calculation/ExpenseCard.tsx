
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { ExpenseCategory } from '@/types/costCalculation';
import { iconMap } from '@/constants/iconMap';

interface ExpenseCardProps {
  expense: ExpenseCategory;
  onExpenseChange: (id: string, field: keyof ExpenseCategory, value: any) => void;
  onRemove?: (id: string) => void;
}

const ExpenseCard = ({ expense, onExpenseChange, onRemove }: ExpenseCardProps) => {
  const IconComponent = iconMap[expense.iconName as keyof typeof iconMap] || iconMap.Calculator;

  return (
    <Card className="p-4">
      <div className="flex items-center gap-2 mb-3">
        <IconComponent className="w-5 h-5 text-blue-600" />
        <h3 className="font-semibold text-sm flex-1">{expense.name}</h3>
        {expense.isCustom && onRemove && (
          <Button
            onClick={() => onRemove(expense.id)}
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        )}
      </div>
      <div className="space-y-2">
        <div>
          <Label className="text-xs">Amount (₹)</Label>
          <Input
            type="number"
            value={expense.monthlyAmount}
            onChange={(e) => onExpenseChange(expense.id, 'monthlyAmount', parseFloat(e.target.value) || 0)}
            className="h-8"
          />
        </div>
        <div>
          <Label className="text-xs">Frequency</Label>
          <select
            value={expense.frequency}
            onChange={(e) => onExpenseChange(expense.id, 'frequency', e.target.value)}
            className="w-full h-8 px-2 border border-gray-300 rounded text-sm"
          >
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
            <option value="one-time">One-time</option>
          </select>
        </div>
        {expense.frequency === 'one-time' && (
          <div>
            <Label className="text-xs">Every X Years</Label>
            <Input
              type="number"
              value={expense.yearsInterval || 1}
              onChange={(e) => onExpenseChange(expense.id, 'yearsInterval', parseFloat(e.target.value) || 1)}
              className="h-8"
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default ExpenseCard;
