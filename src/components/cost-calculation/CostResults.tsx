
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp } from 'lucide-react';
import { formatCurrency } from '@/utils/retirementCalculations';
import { CostResults as CostResultsType } from '@/types/costCalculation';

interface CostResultsProps {
  results: CostResultsType;
  inflationRate: number;
  retirementDuration: number;
}

const CostResults = ({ results, inflationRate, retirementDuration }: CostResultsProps) => {
  return (
    <div className="grid md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Current Cost Estimates
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Monthly Expenses</h3>
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(results.currentMonthlyCost)}
              </p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Annual Expenses</h3>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(results.currentAnnualCost)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Inflation-Adjusted Costs at Retirement</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-orange-50 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Monthly Expenses</h3>
              <p className="text-2xl font-bold text-orange-600">
                {formatCurrency(results.adjustedMonthlyCost)}
              </p>
              <p className="text-sm text-orange-700">
                At {inflationRate}% inflation
              </p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Annual Expenses</h3>
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(results.adjustedAnnualCost)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Total Retirement Cost Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="p-4 bg-red-50 rounded-lg text-center">
              <h3 className="font-bold text-lg mb-2">Total Cost</h3>
              <p className="text-3xl font-bold text-red-600">
                {formatCurrency(results.totalRetirementCost)}
              </p>
              <p className="text-sm text-red-700 mt-1">
                For {retirementDuration} years
              </p>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <h3 className="font-bold mb-2">Cost Breakdown</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Monthly Expenses:</span>
                  <span className="font-semibold">{formatCurrency(results.breakdown.monthly)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Yearly Expenses:</span>
                  <span className="font-semibold">{formatCurrency(results.breakdown.yearly)}</span>
                </div>
                <div className="flex justify-between">
                  <span>One-time Costs:</span>
                  <span className="font-semibold">{formatCurrency(results.breakdown.oneTime)}</span>
                </div>
              </div>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-bold mb-2">Key Insights</h3>
              <div className="space-y-1 text-sm text-gray-700">
                <p>• Plan for {formatCurrency(results.adjustedMonthlyCost)} monthly</p>
                <p>• Major purchases: {formatCurrency(results.oneTimeCosts)}</p>
                <p>• Inflation impact: {((results.adjustedMonthlyCost / results.currentMonthlyCost - 1) * 100).toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CostResults;
