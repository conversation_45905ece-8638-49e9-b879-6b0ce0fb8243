import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts";
import { formatCurrency } from "@/utils/retirementCalculations";
import { useCostCalculationStore } from "@/stores/useCostCalculationStore";
import {
  getSafeDetailedBreakdown,
  getSafeCostValue,
  hasDetailedBreakdown,
} from "@/utils/costDataHelpers";

interface ProjectionChartProps {
  data: any;
  retirementAge: number;
}

const ProjectionChart = ({ data, retirementAge }: ProjectionChartProps) => {
  const { results: costAnalysisData } = useCostCalculationStore();

  // Safe access to detailed breakdown with fallback defaults
  const safeDetailedBreakdown = getSafeDetailedBreakdown(costAnalysisData);
  const hasValidDetailedBreakdown = hasDetailedBreakdown(costAnalysisData);

  // Create enhanced chart data that includes post-retirement projections with cost deductions
  const enhancedChartData = useMemo(() => {
    if (!costAnalysisData) return data.yearlyProjections;

    const retirementStartAge = retirementAge;
    const postRetirementYears = 25; // Default retirement duration
    const annualWithdrawal = costAnalysisData.adjustedAnnualCost || 0;

    let enhancedData = [...data.yearlyProjections];
    let remainingPortfolio = data.finalValue;

    // Add post-retirement projections with annual withdrawals
    for (let i = 1; i <= postRetirementYears; i++) {
      const age = retirementStartAge + i;
      const year = enhancedData[enhancedData.length - 1].year + 1;

      // Assume 4% growth during retirement (conservative estimate)
      remainingPortfolio = remainingPortfolio * 1.04 - annualWithdrawal;

      // Prevent negative portfolio values
      if (remainingPortfolio < 0) remainingPortfolio = 0;

      const realValue = remainingPortfolio / Math.pow(1.06, i); // Adjust for inflation

      enhancedData.push({
        age,
        year,
        portfolioValue: remainingPortfolio,
        realValue,
        cumulativeContributions: data.totalContributions,
        annualCost: annualWithdrawal,
        isRetirement: true,
      });
    }

    return enhancedData;
  }, [costAnalysisData, data, retirementAge]);

  const chartData = useMemo(
    () =>
      enhancedChartData.map((projection: any) => ({
        age: projection.age,
        year: projection.year,
        "Portfolio Value": projection.portfolioValue,
        "Real Value": projection.realValue,
        "Cumulative Contributions": projection.cumulativeContributions,
        "Annual Costs": projection.annualCost || 0,
        phase: projection.isRetirement ? "Retirement" : "Accumulation",
      })),
    [enhancedChartData]
  );

  // Calculate fund depletion age if applicable
  const fundDepletionAge = useMemo(
    () => chartData.find((item) => item["Portfolio Value"] <= 0)?.age,
    [chartData]
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Portfolio Growth & Retirement Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="age"
                  label={{ value: "Age", position: "insideBottom", offset: -5 }}
                />
                <YAxis
                  tickFormatter={formatCurrency}
                  label={{
                    value: "Value (₹)",
                    angle: -90,
                    position: "insideLeft",
                  }}
                />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    formatCurrency(value),
                    name,
                  ]}
                  labelFormatter={(age) => `Age: ${age}`}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="Portfolio Value"
                  stackId="1"
                  stroke="#2563eb"
                  fill="#3b82f6"
                  fillOpacity={0.3}
                />
                <Line
                  type="monotone"
                  dataKey="Real Value"
                  stroke="#dc2626"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={false}
                />
                {costAnalysisData && (
                  <Line
                    type="monotone"
                    dataKey="Annual Costs"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    dot={false}
                  />
                )}
              </AreaChart>
            </ResponsiveContainer>
          </div>
          {fundDepletionAge && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 font-medium">
                ⚠️ Warning: Portfolio may be depleted by age {fundDepletionAge}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Retirement Income Projection</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="font-medium">Monthly Income (4% Rule)</span>
                <span className="text-xl font-bold text-blue-600">
                  {formatCurrency(data.monthlyRetirementIncome)}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="font-medium">
                  Real Monthly Income (Today's Value)
                </span>
                <span className="text-xl font-bold text-green-600">
                  {formatCurrency(data.realMonthlyIncome)}
                </span>
              </div>
              {costAnalysisData && (
                <>
                  <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                    <span className="font-medium">
                      Projected Monthly Expenses
                    </span>
                    <span className="text-xl font-bold text-orange-600">
                      {formatCurrency(costAnalysisData.adjustedMonthlyCost)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                    <span className="font-medium">
                      Projected Yearly Expenses
                    </span>
                    <span className="text-xl font-bold text-yellow-600">
                      {formatCurrency(safeDetailedBreakdown.adjustedYearlyCost)}
                    </span>
                  </div>
                </>
              )}
              <p className="text-sm text-gray-600 mt-2">
                * Based on the 4% withdrawal rule, which suggests withdrawing 4%
                of your portfolio value annually in retirement.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Fund Sustainability Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Total Return:</span>
                <span className="font-bold">
                  {formatCurrency(data.finalValue - data.totalContributions)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Return Multiple:</span>
                <span className="font-bold">
                  {(data.finalValue / data.totalContributions).toFixed(1)}x
                </span>
              </div>
              {costAnalysisData && (
                <>
                  <div className="flex justify-between">
                    <span>Annual Expense Coverage:</span>
                    <span className="font-bold">
                      {(
                        ((data.monthlyRetirementIncome * 12) /
                          costAnalysisData.adjustedAnnualCost) *
                        100
                      ).toFixed(0)}
                      %
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fund Duration:</span>
                    <span className="font-bold">
                      {fundDepletionAge
                        ? `${fundDepletionAge - retirementAge} years`
                        : "25+ years"}
                    </span>
                  </div>
                </>
              )}
            </div>
            {costAnalysisData && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">
                  Income vs Expenses Breakdown
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Monthly Income:</span>
                    <span className="text-green-600">
                      {formatCurrency(data.monthlyRetirementIncome)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Monthly Expenses:</span>
                    <span className="text-red-600">
                      {formatCurrency(costAnalysisData.adjustedMonthlyCost)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yearly Expenses:</span>
                    <span className="text-orange-600">
                      {formatCurrency(safeDetailedBreakdown.adjustedYearlyCost)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>One-time Costs (Total):</span>
                    <span className="text-purple-600">
                      {formatCurrency(costAnalysisData.oneTimeCosts)}
                    </span>
                  </div>
                  <hr className="my-1" />
                  <div className="flex justify-between font-semibold">
                    <span>Net Monthly:</span>
                    <span
                      className={
                        data.monthlyRetirementIncome -
                          costAnalysisData.adjustedMonthlyCost >=
                        0
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {formatCurrency(
                        data.monthlyRetirementIncome -
                          costAnalysisData.adjustedMonthlyCost
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between font-semibold">
                    <span>Annual Coverage Ratio:</span>
                    <span
                      className={
                        data.monthlyRetirementIncome * 12 >=
                        costAnalysisData.adjustedAnnualCost
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {(
                        ((data.monthlyRetirementIncome * 12) /
                          costAnalysisData.adjustedAnnualCost) *
                        100
                      ).toFixed(0)}
                      %
                    </span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed Expense Projections */}
      {costAnalysisData &&
        hasValidDetailedBreakdown &&
        (safeDetailedBreakdown.yearlyExpensesByCategory.length > 0 ||
          safeDetailedBreakdown.oneTimeExpensesByCategory.length > 0) && (
          <div className="grid md:grid-cols-2 gap-6">
            {/* Yearly Expenses Projection */}
            {safeDetailedBreakdown.yearlyExpensesByCategory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Yearly Expenses in Retirement</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {safeDetailedBreakdown.yearlyExpensesByCategory.map(
                      (expense, index) => (
                        <div
                          key={index}
                          className="p-3 bg-yellow-50 rounded-lg"
                        >
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{expense.name}</span>
                            <span className="text-lg font-bold text-yellow-600">
                              {formatCurrency(expense.adjustedAmount)}
                            </span>
                          </div>
                          <div className="text-xs text-yellow-700 mt-1">
                            Current: {formatCurrency(expense.currentAmount)} |
                            Total over {retirementAge === 60 ? 25 : 30} years:{" "}
                            {formatCurrency(
                              expense.adjustedAmount *
                                (retirementAge === 60 ? 25 : 30)
                            )}
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* One-Time Expenses Projection */}
            {safeDetailedBreakdown.oneTimeExpensesByCategory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Fixed & One-Time Expenses</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {safeDetailedBreakdown.oneTimeExpensesByCategory.map(
                      (expense, index) => (
                        <div
                          key={index}
                          className="p-3 bg-purple-50 rounded-lg"
                        >
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{expense.name}</span>
                            <span className="text-lg font-bold text-purple-600">
                              {formatCurrency(expense.totalCost)}
                            </span>
                          </div>
                          <div className="text-xs text-purple-700 mt-1">
                            {formatCurrency(expense.costPerOccurrence)} every{" "}
                            {expense.yearsInterval} years |
                            {expense.totalOccurrences} occurrences total
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

      {!costAnalysisData && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-gray-500">
              <p className="mb-2">
                💡 Complete the Cost Analysis tab to see detailed retirement
                expense projections here.
              </p>
              <p className="text-sm">
                This will show you how your portfolio will be used during
                retirement and whether your savings will last.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProjectionChart;
