
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface RetirementData {
  currentAge: number;
  retirementAge: number;
  currentPortfolio: number;
  monthlyContribution: number;
  annualReturn: number;
  inflationRate: number;
  contributionGrowthRate: number;
  emergencyFund: number;
}

interface RetirementStore {
  data: RetirementData;
  
  // Actions
  setData: (data: RetirementData) => void;
  updateField: (field: keyof RetirementData, value: number) => void;
  resetToDefaults: () => void;
}

const defaultData: RetirementData = {
  currentAge: 30,
  retirementAge: 60,
  currentPortfolio: 500000,
  monthlyContribution: 25000,
  annualReturn: 12,
  inflationRate: 6,
  contributionGrowthRate: 8,
  emergencyFund: 200000,
};

export const useRetirementStore = create<RetirementStore>()(
  persist(
    (set) => ({
      data: defaultData,

      setData: (data) => set({ data }),
      
      updateField: (field, value) => set((state) => ({
        data: { ...state.data, [field]: value }
      })),

      resetToDefaults: () => set({ data: defaultData })
    }),
    {
      name: 'retirement-calculator-storage',
      partialize: (state) => ({
        data: state.data
      })
    }
  )
);
