import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ExpenseCategory, CostResults } from "@/types/costCalculation";
import { defaultExpenses } from "@/constants/defaultExpenses";
import {
  migrateCostResults,
  isValidCostResults,
} from "@/utils/costDataHelpers";

interface CostCalculationStore {
  expenses: ExpenseCategory[];
  retirementDuration: number;
  results: CostResults | null;
  showAddForm: boolean;
  newExpense: Partial<ExpenseCategory>;

  // Actions
  setExpenses: (expenses: ExpenseCategory[]) => void;
  updateExpense: (id: string, field: keyof ExpenseCategory, value: any) => void;
  addExpense: (expense: ExpenseCategory) => void;
  removeExpense: (id: string) => void;
  setRetirementDuration: (duration: number) => void;
  setResults: (results: CostResults) => void;
  setShowAddForm: (show: boolean) => void;
  setNewExpense: (expense: Partial<ExpenseCategory>) => void;
  resetNewExpense: () => void;
  resetToDefaults: () => void;
}

const defaultNewExpense: Partial<ExpenseCategory> = {
  name: "",
  iconName: "Calculator",
  monthlyAmount: 0,
  frequency: "monthly",
  yearsInterval: 1,
};

export const useCostCalculationStore = create<CostCalculationStore>()(
  persist(
    (set, get) => ({
      expenses: defaultExpenses,
      retirementDuration: 25,
      results: null,
      showAddForm: false,
      newExpense: defaultNewExpense,

      setExpenses: (expenses) => set({ expenses }),

      updateExpense: (id, field, value) =>
        set((state) => ({
          expenses: state.expenses.map((expense) =>
            expense.id === id ? { ...expense, [field]: value } : expense
          ),
        })),

      addExpense: (expense) =>
        set((state) => ({
          expenses: [...state.expenses, expense],
        })),

      removeExpense: (id) =>
        set((state) => ({
          expenses: state.expenses.filter((expense) => expense.id !== id),
        })),

      setRetirementDuration: (duration) =>
        set({ retirementDuration: duration }),

      setResults: (results) => {
        // Validate and migrate results to ensure compatibility
        const validatedResults = isValidCostResults(results)
          ? migrateCostResults(results)
          : null;
        set({ results: validatedResults });
      },

      setShowAddForm: (show) => set({ showAddForm: show }),

      setNewExpense: (expense) => set({ newExpense: expense }),

      resetNewExpense: () => set({ newExpense: defaultNewExpense }),

      resetToDefaults: () =>
        set({
          expenses: defaultExpenses,
          retirementDuration: 25,
          results: null,
          showAddForm: false,
          newExpense: defaultNewExpense,
        }),
    }),
    {
      name: "cost-calculation-storage",
      partialize: (state) => ({
        expenses: state.expenses,
        retirementDuration: state.retirementDuration,
        results: state.results,
      }),
      // Migrate old data when loading from storage
      onRehydrateStorage: () => (state) => {
        if (state?.results) {
          // Migrate existing results to new structure
          state.results = migrateCostResults(state.results);
        }
      },
    }
  )
);
