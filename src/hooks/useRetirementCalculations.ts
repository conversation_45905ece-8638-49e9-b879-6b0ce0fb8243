
import { useMemo } from 'react';
import { useRetirementStore } from '@/stores/useRetirementStore';
import { calculateRetirementProjection } from '@/utils/retirementCalculations';

export const useRetirementCalculations = () => {
  const { data, updateField } = useRetirementStore();

  const projections = useMemo(() => {
    const result = calculateRetirementProjection(data);
    console.log('Retirement projections calculated:', result);
    return result;
  }, [data]);

  const handleInputChange = (field: keyof typeof data, value: string) => {
    const numValue = parseFloat(value) || 0;
    updateField(field, numValue);
  };

  return {
    data,
    projections,
    handleInputChange
  };
};
