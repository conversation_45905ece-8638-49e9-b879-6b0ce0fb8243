import { ExpenseCategory, CostResults } from "@/types/costCalculation";
import { useCostCalculationStore } from "@/stores/useCostCalculationStore";
import { useRetirementStore } from "@/stores/useRetirementStore";

export const useCostCalculation = (
  retirementAge: number,
  inflationRate: number
) => {
  const {
    expenses,
    retirementDuration,
    results,
    showAddForm,
    newExpense,
    updateExpense,
    addExpense,
    removeExpense,
    setRetirementDuration,
    setResults,
    setShowAddForm,
    setNewExpense,
    resetNewExpense,
  } = useCostCalculationStore();

  const { data: retirementData } = useRetirementStore();

  const calculateCosts = () => {
    let currentMonthlyCost = 0;
    let currentYearlyCost = 0;
    let currentOneTimeCosts = 0;

    // Calculate current costs by frequency
    expenses.forEach((expense) => {
      if (expense.frequency === "monthly") {
        currentMonthlyCost += expense.monthlyAmount;
      } else if (expense.frequency === "yearly") {
        // For yearly expenses, monthlyAmount actually represents the yearly amount
        currentYearlyCost += expense.monthlyAmount;
      } else if (expense.frequency === "one-time" && expense.yearsInterval) {
        // Calculate how many times this expense will occur during retirement
        // Assume first occurrence happens at the start of retirement (year 0)
        // Then subsequent occurrences happen every yearsInterval years
        let occurrences = 0;
        for (
          let year = 0;
          year < retirementDuration;
          year += expense.yearsInterval
        ) {
          occurrences++;
        }
        currentOneTimeCosts += expense.monthlyAmount * occurrences;
      }
    });

    // Calculate years to retirement
    const yearsToRetirement = retirementAge - retirementData.currentAge;

    // Apply inflation to get costs at retirement start
    const inflationMultiplierAtRetirement = Math.pow(
      1 + inflationRate / 100,
      yearsToRetirement
    );
    const adjustedMonthlyCostAtRetirement =
      currentMonthlyCost * inflationMultiplierAtRetirement;
    const adjustedYearlyCostAtRetirement =
      currentYearlyCost * inflationMultiplierAtRetirement;
    const adjustedOneTimeCostsAtRetirement =
      currentOneTimeCosts * inflationMultiplierAtRetirement;

    // Calculate total retirement costs with progressive inflation during retirement
    let totalMonthlyExpenses = 0;
    let totalYearlyExpenses = 0;
    let totalOneTimeExpenses = 0;

    // Calculate one-time expenses with inflation applied at the time they occur
    expenses.forEach((expense) => {
      if (expense.frequency === "one-time" && expense.yearsInterval) {
        for (
          let year = 0;
          year < retirementDuration;
          year += expense.yearsInterval
        ) {
          const yearInflationMultiplier = Math.pow(
            1 + inflationRate / 100,
            yearsToRetirement + year
          );
          const adjustedExpenseAtOccurrence =
            expense.monthlyAmount * yearInflationMultiplier;
          totalOneTimeExpenses += adjustedExpenseAtOccurrence;
        }
      }
    });

    // Calculate monthly and yearly expenses with inflation for each year of retirement
    for (let year = 0; year < retirementDuration; year++) {
      const yearInflationMultiplier = Math.pow(1 + inflationRate / 100, year);
      const yearlyMonthlyCost =
        adjustedMonthlyCostAtRetirement * yearInflationMultiplier * 12;
      const yearlyYearlyCost =
        adjustedYearlyCostAtRetirement * yearInflationMultiplier;

      totalMonthlyExpenses += yearlyMonthlyCost;
      totalYearlyExpenses += yearlyYearlyCost;
    }

    const totalRetirementCost =
      totalMonthlyExpenses + totalYearlyExpenses + totalOneTimeExpenses;
    // Calculate average annual cost (for display purposes)
    // Note: One-time costs are amortized over retirement duration for comparison
    const averageOneTimeCostPerYear = currentOneTimeCosts / retirementDuration;
    const currentAnnualCost =
      currentMonthlyCost * 12 + currentYearlyCost + averageOneTimeCostPerYear;

    const averageAdjustedOneTimeCostPerYear =
      totalOneTimeExpenses / retirementDuration;
    const adjustedAnnualCost =
      adjustedMonthlyCostAtRetirement * 12 +
      adjustedYearlyCostAtRetirement +
      averageAdjustedOneTimeCostPerYear;

    // Prepare detailed breakdown for projections
    const yearlyExpensesByCategory = expenses
      .filter((expense) => expense.frequency === "yearly")
      .map((expense) => ({
        name: expense.name,
        currentAmount: expense.monthlyAmount,
        adjustedAmount: expense.monthlyAmount * inflationMultiplierAtRetirement,
        frequency: "yearly" as const,
      }));

    const oneTimeExpensesByCategory = expenses
      .filter(
        (expense) => expense.frequency === "one-time" && expense.yearsInterval
      )
      .map((expense) => {
        let occurrences = 0;
        for (
          let year = 0;
          year < retirementDuration;
          year += expense.yearsInterval!
        ) {
          occurrences++;
        }

        let totalCost = 0;
        for (
          let year = 0;
          year < retirementDuration;
          year += expense.yearsInterval!
        ) {
          const yearInflationMultiplier = Math.pow(
            1 + inflationRate / 100,
            yearsToRetirement + year
          );
          totalCost += expense.monthlyAmount * yearInflationMultiplier;
        }

        return {
          name: expense.name,
          costPerOccurrence: expense.monthlyAmount,
          yearsInterval: expense.yearsInterval!,
          totalOccurrences: occurrences,
          totalCost,
          frequency: "one-time" as const,
        };
      });

    const newResults: CostResults = {
      currentMonthlyCost,
      currentAnnualCost,
      adjustedMonthlyCost: adjustedMonthlyCostAtRetirement,
      adjustedAnnualCost,
      totalRetirementCost,
      oneTimeCosts: totalOneTimeExpenses, // Use the properly calculated total
      breakdown: {
        monthly: totalMonthlyExpenses,
        yearly: totalYearlyExpenses,
        oneTime: totalOneTimeExpenses,
      },
      detailedBreakdown: {
        currentYearlyCost,
        adjustedYearlyCost: adjustedYearlyCostAtRetirement,
        yearlyExpensesByCategory,
        oneTimeExpensesByCategory,
      },
    };

    setResults(newResults);

    console.log("Cost calculation results with retirement inflation:", {
      currentMonthlyCost,
      currentYearlyCost,
      currentOneTimeCosts,
      adjustedMonthlyCostAtRetirement,
      totalMonthlyExpenses,
      totalYearlyExpenses,
      totalOneTimeExpenses,
      totalRetirementCost,
      yearsToRetirement,
      retirementDuration,
    });
  };

  const addCustomExpense = () => {
    if (!newExpense.name || !newExpense.monthlyAmount) {
      return;
    }

    // Validate one-time expenses have yearsInterval
    if (
      newExpense.frequency === "one-time" &&
      (!newExpense.yearsInterval || newExpense.yearsInterval <= 0)
    ) {
      console.error("One-time expenses must have a valid years interval");
      return;
    }

    const customExpense: ExpenseCategory = {
      id: Date.now().toString(),
      name: newExpense.name,
      iconName: newExpense.iconName || "Calculator",
      monthlyAmount: newExpense.monthlyAmount,
      frequency: newExpense.frequency || "monthly",
      yearsInterval: newExpense.yearsInterval,
      isCustom: true,
    };

    addExpense(customExpense);
    resetNewExpense();
    setShowAddForm(false);
  };

  return {
    expenses,
    retirementDuration,
    results,
    showAddForm,
    newExpense,
    setRetirementDuration,
    calculateCosts,
    setShowAddForm,
    setNewExpense,
    addCustomExpense,
    handleExpenseChange: updateExpense,
    handleAddExpense: addExpense,
    handleRemoveExpense: removeExpense,
  };
};
