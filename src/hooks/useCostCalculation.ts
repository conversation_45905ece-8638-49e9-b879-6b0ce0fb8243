
import { ExpenseCategory, CostResults } from '@/types/costCalculation';
import { useCostCalculationStore } from '@/stores/useCostCalculationStore';
import { useRetirementStore } from '@/stores/useRetirementStore';

export const useCostCalculation = (retirementAge: number, inflationRate: number) => {
  const {
    expenses,
    retirementDuration,
    results,
    showAddForm,
    newExpense,
    updateExpense,
    addExpense,
    removeExpense,
    setRetirementDuration,
    setResults,
    setShowAddForm,
    setNewExpense,
    resetNewExpense
  } = useCostCalculationStore();

  const { data: retirementData } = useRetirementStore();

  const calculateCosts = () => {
    let currentMonthlyCost = 0;
    let currentYearlyCost = 0;
    let currentOneTimeCosts = 0;

    // Calculate current costs by frequency
    expenses.forEach(expense => {
      if (expense.frequency === 'monthly') {
        currentMonthlyCost += expense.monthlyAmount;
      } else if (expense.frequency === 'yearly') {
        currentYearlyCost += expense.monthlyAmount;
      } else if (expense.frequency === 'one-time' && expense.yearsInterval) {
        const occurrences = Math.floor(retirementDuration / expense.yearsInterval);
        currentOneTimeCosts += expense.monthlyAmount * occurrences;
      }
    });

    // Calculate years to retirement
    const yearsToRetirement = retirementAge - retirementData.currentAge;
    
    // Apply inflation to get costs at retirement start
    const inflationMultiplierAtRetirement = Math.pow(1 + inflationRate / 100, yearsToRetirement);
    const adjustedMonthlyCostAtRetirement = currentMonthlyCost * inflationMultiplierAtRetirement;
    const adjustedYearlyCostAtRetirement = currentYearlyCost * inflationMultiplierAtRetirement;
    const adjustedOneTimeCostsAtRetirement = currentOneTimeCosts * inflationMultiplierAtRetirement;

    // Calculate total retirement costs with progressive inflation during retirement
    let totalMonthlyExpenses = 0;
    let totalYearlyExpenses = 0;
    let totalOneTimeExpenses = adjustedOneTimeCostsAtRetirement;

    // Calculate monthly expenses with inflation for each year of retirement
    for (let year = 0; year < retirementDuration; year++) {
      const yearInflationMultiplier = Math.pow(1 + inflationRate / 100, year);
      const yearlyMonthlyCost = adjustedMonthlyCostAtRetirement * yearInflationMultiplier * 12;
      const yearlyYearlyCost = adjustedYearlyCostAtRetirement * yearInflationMultiplier;
      
      totalMonthlyExpenses += yearlyMonthlyCost;
      totalYearlyExpenses += yearlyYearlyCost;
    }

    const totalRetirementCost = totalMonthlyExpenses + totalYearlyExpenses + totalOneTimeExpenses;
    const currentAnnualCost = (currentMonthlyCost * 12) + currentYearlyCost + (currentOneTimeCosts / retirementDuration);
    const adjustedAnnualCost = (adjustedMonthlyCostAtRetirement * 12) + adjustedYearlyCostAtRetirement + (adjustedOneTimeCostsAtRetirement / retirementDuration);

    const newResults: CostResults = {
      currentMonthlyCost,
      currentAnnualCost,
      adjustedMonthlyCost: adjustedMonthlyCostAtRetirement,
      adjustedAnnualCost,
      totalRetirementCost,
      oneTimeCosts: adjustedOneTimeCostsAtRetirement,
      breakdown: {
        monthly: totalMonthlyExpenses,
        yearly: totalYearlyExpenses,
        oneTime: totalOneTimeExpenses
      }
    };

    setResults(newResults);

    console.log('Cost calculation results with retirement inflation:', {
      currentMonthlyCost,
      currentYearlyCost,
      currentOneTimeCosts,
      adjustedMonthlyCostAtRetirement,
      totalMonthlyExpenses,
      totalYearlyExpenses,
      totalOneTimeExpenses,
      totalRetirementCost,
      yearsToRetirement,
      retirementDuration
    });
  };

  const addCustomExpense = () => {
    if (!newExpense.name || !newExpense.monthlyAmount) {
      return;
    }

    const customExpense: ExpenseCategory = {
      id: Date.now().toString(),
      name: newExpense.name,
      iconName: newExpense.iconName || 'Calculator',
      monthlyAmount: newExpense.monthlyAmount,
      frequency: newExpense.frequency || 'monthly',
      yearsInterval: newExpense.yearsInterval,
      isCustom: true,
    };

    addExpense(customExpense);
    resetNewExpense();
    setShowAddForm(false);
  };

  return {
    expenses,
    retirementDuration,
    results,
    showAddForm,
    newExpense,
    setRetirementDuration,
    calculateCosts,
    setShowAddForm,
    setNewExpense,
    addCustomExpense,
    handleExpenseChange: updateExpense,
    handleAddExpense: addExpense,
    handleRemoveExpense: removeExpense
  };
};
